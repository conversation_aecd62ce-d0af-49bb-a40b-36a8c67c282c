import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Invalid credentials');
        }

        try {
          // Call the Go backend login API
          const response = await fetch(`${process.env.GO_BACKEND_URL || 'http://localhost:8050'}/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          });

          if (!response.ok) {
            console.error('Go backend auth error:', response.status, response.statusText);
            return null;
          }

          const result = await response.json();

          if (!result.user) {
            console.error('No user data returned from Go backend');
            return null;
          }

          // Return user data in NextAuth format
          return {
            id: result.user.id,
            email: result.user.email,
            name: result.user.name,
            image: result.user.image || null,
            // Add any additional fields you need
          };
        } catch (error) {
          console.error('Error during authentication:', error);
          return null;
        }
      }
    })
  ],
  pages: {
    signIn: '/login',
    signOut: '/login',
    error: '/login',
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === 'google') {
        try {
          // For Google OAuth, we'll create the user in the Go backend
          // The Go backend should handle user creation and organization setup
          console.log(`Google sign-in for user: ${user.email}`);

          // You could optionally call the Go backend to ensure the user exists there
          // For now, we'll let NextAuth handle the session and the Go backend
          // will create the user on first API call if needed

          return true;
        } catch (error) {
          console.error('Error in Google sign-in:', error);
          return true; // Allow login to proceed
        }
      }
      return true;
    },
    async jwt({ token, user, trigger, session }) {
      // Initial sign in
      if (user) {
        token.userId = user.id;
        token.email = user.email || '';
        token.name = user.name || '';
        token.merchantId = (user as any).merchantId || '';
        token.defaultMerchantId = (user as any).defaultMerchantId || '';
        token.picture = user.image || '';
      }

      // Handle session updates (e.g., switching merchants)
      if (trigger === 'update' && session) {
        token.merchantId = session.merchantId || token.merchantId;
        token.defaultMerchantId = session.defaultMerchantId || token.defaultMerchantId;
      }

      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.userId as string;
        session.user.email = token.email as string;
        session.user.name = token.name as string;
        session.user.merchantId = token.merchantId as string;
        session.user.defaultMerchantId = token.defaultMerchantId as string;
        session.user.image = token.picture as string;
      }
      return session;
    }
  },
  debug: process.env.NODE_ENV === 'development',
};
