{"config": {"configFile": "/Users/<USER>/Desktop/adc/adc-account-web/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/e2e-results.json"}], ["junit", {"outputFile": "test-results/e2e-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "dashboard/dashboard-complete-flow.spec.ts", "file": "dashboard/dashboard-complete-flow.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Complete Dashboard E2E Flow", "file": "dashboard/dashboard-complete-flow.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should complete full login to dashboard flow", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 10297, "errors": [], "stdout": [{"text": "🚀 Starting complete E2E flow: Login → Dashboard\n"}, {"text": "📍 Step 1: Navigate to login page\n"}, {"text": "✅ Login page loaded\n"}, {"text": "📍 Step 2: Fill in login credentials\n"}, {"text": "✅ Credentials filled\n"}, {"text": "📍 Step 3: Submit login form\n"}, {"text": "✅ Login form submitted\n"}, {"text": "📍 Step 4: Wait for login response\n"}, {"text": "Current URL after login: http://localhost:3000/en/login\n"}, {"text": "📧 Login requires email verification - attempting dashboard access\n"}, {"text": "URL after dashboard navigation: http://localhost:3000/en/login?callbackUrl=%2Fen%2Fdashboard\n"}, {"text": "🔒 Dashboard access blocked - email verification required\n"}, {"text": "✅ Security working correctly - unverified users redirected with callback URL\n"}, {"text": "✅ Authentication flow working correctly\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:28:58.126Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "17b2a5fa342cbceafbb4-7b476b94ed246ac97986", "file": "dashboard/dashboard-complete-flow.spec.ts", "line": 10, "column": 7}, {"title": "should handle complete flow on mobile", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 11573, "errors": [], "stdout": [{"text": "📱 Starting mobile E2E flow: Login → Dashboard\n"}, {"text": "📱 Mobile viewport set\n"}, {"text": "✅ Mobile login page loaded\n"}, {"text": "✅ Mobile credentials filled\n"}, {"text": "Mobile URL after login: http://localhost:3000/en/login\n"}, {"text": "Final mobile URL: http://localhost:3000/en/login?callbackUrl=%2Fen%2Fdashboard\n"}, {"text": "📱 Mobile E2E flow completed\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:28:58.112Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "17b2a5fa342cbceafbb4-c1d9cf4359c0d9679722", "file": "dashboard/dashboard-complete-flow.spec.ts", "line": 133, "column": 7}, {"title": "should test complete flow with invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 9420, "errors": [], "stdout": [{"text": "🔐 Testing complete flow with invalid credentials\n"}, {"text": "✅ Invalid credentials correctly rejected\n"}, {"text": "✅ Unauthenticated dashboard access correctly blocked\n"}, {"text": "🔐 Invalid credentials flow test completed\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:28:58.146Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "17b2a5fa342cbceafbb4-002fa2a81894f6cd0296", "file": "dashboard/dashboard-complete-flow.spec.ts", "line": 176, "column": 7}, {"title": "should test registration to dashboard flow", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 12168, "errors": [], "stdout": [{"text": "📝 Testing registration to dashboard flow\n"}, {"text": "URL after registration: http://localhost:3000/en/register\n"}, {"text": "Dashboard access URL: http://localhost:3000/en/login?callbackUrl=%2Fen%2Fdashboard\n"}, {"text": "📝 Registration flow test completed\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:28:58.134Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "17b2a5fa342cbceafbb4-dfe14e599587b509cd08", "file": "dashboard/dashboard-complete-flow.spec.ts", "line": 209, "column": 7}, {"title": "should test complete user journey with navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 14951, "errors": [], "stdout": [{"text": "🗺️  Testing complete user journey with navigation\n"}, {"text": "✅ Started at home page\n"}, {"text": "✅ Navigated to login page\n"}, {"text": "✅ Registration link found\n"}, {"text": "Current URL: http://localhost:3000/en/login\n"}, {"text": "Navigation to /en/dashboard: http://localhost:3000/en/login?callbackUrl=%2Fen%2Fdashboard\n"}, {"text": "Navigation to /en/organizations: http://localhost:3000/en/login?callbackUrl=%2Fen%2Forganizations\n"}, {"text": "Navigation to /en/profile: http://localhost:3000/en/login?callbackUrl=%2Fen%2Fprofile\n"}, {"text": "🗺️  Complete user journey test finished\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:29:07.756Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "17b2a5fa342cbceafbb4-cd798eb946c5f96350f0", "file": "dashboard/dashboard-complete-flow.spec.ts", "line": 267, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-25T19:28:56.656Z", "duration": 26078.771, "expected": 5, "skipped": 0, "unexpected": 0, "flaky": 0}}