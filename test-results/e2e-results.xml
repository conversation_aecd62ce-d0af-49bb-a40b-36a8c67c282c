<testsuites id="" name="" tests="5" failures="0" skipped="0" errors="0" time="26.078771">
<testsuite name="dashboard/dashboard-complete-flow.spec.ts" timestamp="2025-05-25T19:28:57.520Z" hostname="chromium" tests="5" failures="0" skipped="0" time="58.409" errors="0">
<testcase name="Complete Dashboard E2E Flow › should complete full login to dashboard flow" classname="dashboard/dashboard-complete-flow.spec.ts" time="10.297">
<system-out>
<![CDATA[🚀 Starting complete E2E flow: Login → Dashboard
📍 Step 1: Navigate to login page
✅ Login page loaded
📍 Step 2: Fill in login credentials
✅ Credentials filled
📍 Step 3: Submit login form
✅ Login form submitted
📍 Step 4: Wait for login response
Current URL after login: http://localhost:3000/en/login
📧 Login requires email verification - attempting dashboard access
URL after dashboard navigation: http://localhost:3000/en/login?callbackUrl=%2Fen%2Fdashboard
🔒 Dashboard access blocked - email verification required
✅ Security working correctly - unverified users redirected with callback URL
✅ Authentication flow working correctly
]]>
</system-out>
</testcase>
<testcase name="Complete Dashboard E2E Flow › should handle complete flow on mobile" classname="dashboard/dashboard-complete-flow.spec.ts" time="11.573">
<system-out>
<![CDATA[📱 Starting mobile E2E flow: Login → Dashboard
📱 Mobile viewport set
✅ Mobile login page loaded
✅ Mobile credentials filled
Mobile URL after login: http://localhost:3000/en/login
Final mobile URL: http://localhost:3000/en/login?callbackUrl=%2Fen%2Fdashboard
📱 Mobile E2E flow completed
]]>
</system-out>
</testcase>
<testcase name="Complete Dashboard E2E Flow › should test complete flow with invalid credentials" classname="dashboard/dashboard-complete-flow.spec.ts" time="9.42">
<system-out>
<![CDATA[🔐 Testing complete flow with invalid credentials
✅ Invalid credentials correctly rejected
✅ Unauthenticated dashboard access correctly blocked
🔐 Invalid credentials flow test completed
]]>
</system-out>
</testcase>
<testcase name="Complete Dashboard E2E Flow › should test registration to dashboard flow" classname="dashboard/dashboard-complete-flow.spec.ts" time="12.168">
<system-out>
<![CDATA[📝 Testing registration to dashboard flow
URL after registration: http://localhost:3000/en/register
Dashboard access URL: http://localhost:3000/en/login?callbackUrl=%2Fen%2Fdashboard
📝 Registration flow test completed
]]>
</system-out>
</testcase>
<testcase name="Complete Dashboard E2E Flow › should test complete user journey with navigation" classname="dashboard/dashboard-complete-flow.spec.ts" time="14.951">
<system-out>
<![CDATA[🗺️  Testing complete user journey with navigation
✅ Started at home page
✅ Navigated to login page
✅ Registration link found
Current URL: http://localhost:3000/en/login
Navigation to /en/dashboard: http://localhost:3000/en/login?callbackUrl=%2Fen%2Fdashboard
Navigation to /en/organizations: http://localhost:3000/en/login?callbackUrl=%2Fen%2Forganizations
Navigation to /en/profile: http://localhost:3000/en/login?callbackUrl=%2Fen%2Fprofile
🗺️  Complete user journey test finished
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>